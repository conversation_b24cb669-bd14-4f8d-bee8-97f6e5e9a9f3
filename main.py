# paint2gen_lcm_app/main.py
from fastapi import FastAPI, File, UploadFile, Form
from fastapi.responses import StreamingResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from io import BytesIO
from PIL import Image, ImageOps
import torch
from diffusers import StableDiffusionControlNetPipeline, ControlNetModel, LCMScheduler

app = FastAPI()

# CORS for frontend testing
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for frontend assets (CSS, JS, etc.)
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# Serve the main HTML file for the root path
@app.get("/")
async def read_root():
    return FileResponse("frontend/index.html")

# Load ControlNet for scribble guidance (LCM compatible)
controlnet = ControlNetModel.from_pretrained(
    "lllyasviel/sd-controlnet-scribble",
    torch_dtype=torch.float16
)

# Lykon/dreamshaper-8-lcm
# SimianLuo/LCM_Dreamshaper_v7

# Load LCM pipeline with ControlNet
pipe = StableDiffusionControlNetPipeline.from_pretrained(
    "Lykon/dreamshaper-8-lcm",
    controlnet=controlnet,
    torch_dtype=torch.float16,
    safety_checker=None,  # Disable safety checker for better performance
    local_files_only=False,  # Ensure it downloads the model if not cached
    requires_safety_checker=False  # Disable safety checker completely
)

# Configure the scheduler
pipe.scheduler = LCMScheduler.from_config(pipe.scheduler.config)
pipe.enable_xformers_memory_efficient_attention()
# Move the entire pipeline to GPU for maximum speed
pipe.to("cuda")

# Enable attention slicing for better memory usage
#pipe.enable_attention_slicing()

# This endpoint must be defined before any catch-all routes
@app.post("/generate")
async def generate_image(file: UploadFile = File(...), prompt: str = Form(...)):
    print("Received request with prompt:", prompt)  # Debug log
    init_image = Image.open(BytesIO(await file.read())).convert("RGB").resize((512, 512))

    # Convert to grayscale
    processed_image = init_image.convert("L")
    
    # Enhance contrast to make scribbles more prominent
    from PIL import ImageEnhance
    enhancer = ImageEnhance.Contrast(processed_image)
    processed_image = enhancer.enhance(2.0)  # Increase contrast
    
    # Apply threshold to make lines more distinct
    processed_image = processed_image.point(lambda x: 0 if x < 200 else 255, '1')
    
    # Convert back to RGB and invert (ControlNet expects white lines on black background)
    processed_image = processed_image.convert("RGB")
    processed_image = ImageOps.invert(processed_image)
    
    # Save the processed image for debugging
    processed_image.save("debug_processed.png")  # Uncomment to debug
    
    # Generate with optimized control parameters
    output = pipe(
        prompt=prompt,
        negative_prompt="nsfw, low quality, blurry, distorted, bad anatomy, disfigured, text, watermark, signature",
        image=processed_image,
        num_inference_steps=4,  # Fewer steps for faster generation with LCM
        guidance_scale=2.5,  # Lower guidance scale for more creative freedom
        controlnet_conditioning_scale=1.2,  # Increased ControlNet strength
        width=768,
        height=768,
        generator=torch.manual_seed(42),  # For reproducibility
        control_guidance_start=0.0,  # Start ControlNet guidance immediately
        control_guidance_end=1.0,  # Keep ControlNet guidance until the end
        guess_mode=True  # Disable guess mode for stronger control
    )
    result = output.images[0]

    buf = BytesIO()
    result.save(buf, format="PNG")
    buf.seek(0)
    return StreamingResponse(buf, media_type="image/png")

# Catch-all route to serve frontend files (must be the last route)
@app.get("/{full_path:path}")
async def serve_frontend(full_path: str):
    try:
        return FileResponse(f"frontend/{full_path}")
    except Exception as e:
        # If the file doesn't exist, return the main index.html
        return FileResponse("frontend/index.html")
