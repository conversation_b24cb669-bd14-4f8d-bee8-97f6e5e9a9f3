---
title: Paint2Gen LCM
emoji: 🎨
colorFrom: indigo
colorTo: green
sdk: docker
app_file: main.py
pinned: false
---

# Paint2Gen LCM App 🎨⚡

A FastAPI-based web app that lets users draw an image and generate an AI-enhanced version using ControlNet + LCM.

## Features

- 🖌️ Users draw a sketch in the browser
- ⚙️ Backend receives it along with a prompt
- 🎨 Generates using `LCM` + `ControlNet Scribble`

## Local Setup

```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/paint2gen-lcm
cd paint2gen-lcm
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
uvicorn main:app --reload
```

## Run on Hugging Face Spaces

1. Create a Space → Select Docker → Upload this repo
2. Open the Space — draw and generate!

💡 For best results, sketch in black lines on white background (like a scribble).