<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Paint & Generate</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Inter', Arial, sans-serif;
      background: #f5f6fa;
      color: #23272f;
    }
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    .app-container {
      display: flex;
      flex: 1;
      min-height: 100vh;
      max-width: 1600px;
      margin: 0 auto;
      box-shadow: 0 4px 32px rgba(44,62,80,0.12);
      border-radius: 18px;
      background: #fff;
      overflow: hidden;
      margin-top: 32px;
    }
    .sidebar {
      background: linear-gradient(135deg,#3b82f6 0%, #6366f1 100%);
      color: #fff;
      padding: 32px 24px 24px 24px;
      width: 320px;
      display: flex;
      flex-direction: column;
      gap: 32px;
      box-shadow: 2px 0 16px rgba(44,62,80,0.05);
    }
    .sidebar h1 {
      font-size: 2rem;
      font-weight: 600;
      margin: 0 0 12px 0;
      letter-spacing: -1px;
    }
    .sidebar p {
      font-size: 1rem;
      opacity: 0.85;
      margin-bottom: 0;
    }
    .controls {
      display: flex;
      flex-direction: column;
      gap: 18px;
    }
    .prompt-input {
      padding: 12px 16px;
      border-radius: 8px;
      border: none;
      font-size: 1rem;
      margin-bottom: 6px;
      width: 100%;
      box-shadow: 0 2px 8px rgba(44,62,80,0.04);
    }
    .btn {
      background: #fff;
      color: #3b82f6;
      font-weight: 600;
      border: none;
      border-radius: 8px;
      padding: 10px 18px;
      font-size: 1rem;
      box-shadow: 0 2px 8px rgba(44,62,80,0.04);
      cursor: pointer;
      transition: background 0.2s, color 0.2s;
    }
    .btn:hover {
      background: #3b82f6;
      color: #fff;
    }
    .main-panel {
      display: flex;
      flex-direction: row;
      gap: 40px;
      align-items: flex-start;
      justify-content: center;
      padding: 48px 32px 36px 32px;
      background: #f5f6fa;
      min-width: 0;
      width: 100%;
      box-sizing: border-box;
    }
    .canvas-container, .result-container {
      width: 640px;
      height: 640px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(44,62,80,0.05);
      padding: 16px;
      box-sizing: border-box;
    }
    .result-container {
      justify-content: flex-start;
      min-width: 0;
      min-height: 0;
    }
    .result-container img {
      max-width: 100%;
      max-height: 100%;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(44,62,80,0.07);
      margin-top: 8px;
      display: block;
    }
    @media (max-width: 1400px) {
      .main-panel { flex-direction: column; gap: 24px; align-items: center; }
      .result-container, .canvas-container { width: 98vw; min-width: 0; min-height: 320px; height: auto; }
    }
    .canvas-container {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(44,62,80,0.05);
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
    #canvas {
      border-radius: 10px;
      background: #fff;
      box-shadow: 0 1px 6px rgba(44,62,80,0.07);
      border: 1.5px solid #e5e7eb;
      cursor: crosshair;
      margin-bottom: 8px;
      width: 640px;
      height: 640px;
      max-width: 98vw;
      max-height: 80vw;
      touch-action: none;
      display: block;
    }
    .canvas-controls {
      display: flex;
      gap: 12px;
    }
    .result-container {
      margin-top: 12px;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(44,62,80,0.05);
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 180px;
      min-width: 0;
    }
    .result-container img {
      max-width: 100%;
      max-height: 320px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(44,62,80,0.07);
      margin-top: 8px;
    }
    @media (max-width: 900px) {
      .app-container { flex-direction: column; box-shadow: none; border-radius: 0; margin-top: 0; }
      .sidebar { width: 100%; border-radius: 0; box-shadow: none; padding: 24px 18px; }
      .main-panel { padding: 18px 6vw 18px 6vw; }
      #canvas { width: 90vw; height: 90vw; max-width: 95vw; max-height: 80vw; }
    }
    @media (max-width: 600px) {
      .main-panel { padding: 12px 2vw; }
      .sidebar { padding: 16px 8px; }
      #canvas { width: 98vw; height: 98vw; }
    }
  </style>
  <style>
    canvas { border: 1px solid black; cursor: crosshair; }
    #controls { margin-top: 10px; }
  </style>
</head>
<body>
  <div class="app-container">
    <aside class="sidebar">
      <div>
        <h1>Paint2Gen LCM</h1>
        <p>
          Draw a scribble, enter a prompt, and watch your idea turn into AI art in real time.<br>
          <span class="slogan-powered">Powered by ControlNet + LCM Dreamshaper.</span>
        </p>
      </div>
      <div class="controls">
        <input id="prompt" class="prompt-input" type="text" placeholder="Describe your image...">
        <button class="btn" id="generate-btn" onclick="sendToBackend()">Generate</button>
        <button class="btn btn-clear" onclick="clearCanvas()">Clear</button>
      </div>
      <div class="footer-info">
        <span>Made with ❤️ for AI art</span>
      </div>
    </aside>
    <main class="main-panel">
      <div class="canvas-container canvas-container-rel">
        <div id="spinner-corner">
          <div class="spinner"></div>
        </div>
        <canvas id="canvas" width="640" height="640" class="canvas-rel"></canvas>
        <div class="canvas-controls">
          <span class="canvas-controls-info">Draw with your mouse or finger</span>
        </div>
      </div>
      <div class="result-container" id="result">
        <span class="result-placeholder">Your generated image will appear here...</span>
      </div>
    </main>
  </div>
  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    // Fill the canvas with white background on load
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    let painting = false;

    canvas.addEventListener('mousedown', () => painting = true);
    canvas.addEventListener('mouseup', () => {
      painting = false;
      ctx.beginPath();
    });
    // Throttle utility
    function throttle(func, delay) {
      let lastCall = 0;
      let timeout;
      return function(...args) {
        const now = Date.now();
        if (now - lastCall < delay) {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            lastCall = Date.now();
            func.apply(this, args);
          }, delay);
        } else {
          lastCall = now;
          func.apply(this, args);
        }
      };
    }

    // Real-time generation on mousemove
    canvas.addEventListener('mousemove', function(e) {
      draw(e);
      if (painting) throttledSendToBackend();
    });

    // Throttled version of sendToBackend
    const throttledSendToBackend = throttle(sendToBackend, 500); // 500ms between requests

    function draw(e) {
      if (!painting) return;
      ctx.lineWidth = 6;
      ctx.lineCap = 'round';
      ctx.strokeStyle = 'black';
      ctx.lineTo(e.offsetX, e.offsetY);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(e.offsetX, e.offsetY);
    }

    function clearCanvas() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
    }

    function sendToBackend() {
      // Show spinner and disable Generate button
      document.getElementById('spinner-corner').style.display = 'block';
      document.getElementById('generate-btn').disabled = true;
      canvas.toBlob(async blob => {
        const formData = new FormData();
        formData.append('file', blob, 'painting.png');
        formData.append('prompt', document.getElementById('prompt').value);

        try {
          const response = await fetch('http://localhost:5000/generate', {
            method: 'POST',
            body: formData
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} ${response.statusText} - ${errorText}`);
          }
          
          const resultBlob = await response.blob();
          const img = new Image();
          img.src = URL.createObjectURL(resultBlob);
          const resultDiv = document.getElementById('result');
          resultDiv.innerHTML = '';
          resultDiv.appendChild(img);
        } catch (error) {
          console.error('Error:', error);
          const resultDiv = document.getElementById('result');
          resultDiv.innerHTML = `<p style=\"color: red;\">Error: ${error.message}</p>`;
        } finally {
          document.getElementById('spinner-corner').style.display = 'none';
          document.getElementById('generate-btn').disabled = false;
        }
      });
    }
  </script>
</body>
</html>